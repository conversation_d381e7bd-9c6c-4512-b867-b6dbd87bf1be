# 客户信息批量查询工具

这个工具用于批量查询客户信息，通过Excel文件中的会员号列表进行查询，并生成包含详细客户信息的Excel报告。

## 功能说明

1. 生成Excel模板，用于填写会员号
2. 批量查询多个客户的信息
3. 提取客户的基本信息和标签信息
4. 生成包含客户信息和标签信息的Excel报告

## 使用前准备

安装必要的依赖：

```bash
pip install pandas openpyxl requests
```

## 使用方法

### 第一步：生成Excel模板

直接运行脚本，不带任何参数：

```bash
python excel_batch_processor.py
```

这将生成一个名为`会员号模板.xlsx`的Excel文件。

### 第二步：填写会员号

打开生成的Excel模板，在"会员号"列中填入要查询的会员号，可以填入多行进行批量查询。

### 第三步：运行批量查询

使用填写好的Excel文件运行脚本：

```bash
python excel_batch_processor.py 会员号模板.xlsx
```

或者指定其他Excel文件：

```bash
python excel_batch_processor.py 你的文件名.xlsx
```

### 第四步：查看结果

脚本执行完成后，将生成一个名为`客户信息报告_时间戳.xlsx`的Excel文件，包含以下内容：

1. "客户信息"工作表：包含客户的基本信息（ID、姓名、会员号、手机号、性别等）
2. "标签信息"工作表：包含所有客户关联的标签详细信息

## 注意事项

1. Excel文件必须包含名为"会员号"的列
2. 每次运行脚本将自动获取新的Token
3. 查询结果会保存在当前目录下的Excel文件中
4. 如果某个会员号查询失败，会在控制台输出错误信息，但不会影响其他客户的查询

## 示例

查询结果示例：

### 客户信息表
| id | name | code | mobile | gender | birthday | email | province | city | district | addressDetail | tagsCount |
|----|------|------|--------|--------|----------|-------|----------|------|----------|--------------|-----------|
| a1b2c3d4e5f6 | 诸葛建华 | 00001053 | 13812345678 | 男 | 1980-01-01 | <EMAIL> | 上海市 | 上海市 | 浦东新区 | 张江高科技园区 | 2 |

### 标签信息表
| customerId | customerName | customerCode | tagId | tagName | tagColor |
|------------|--------------|--------------|-------|---------|----------|
| a1b2c3d4e5f6 | 诸葛建华 | 00001053 | t1 | VIP客户 | #FF0000 |
| a1b2c3d4e5f6 | 诸葛建华 | 00001053 | t2 | 老客户 | #0000FF | 