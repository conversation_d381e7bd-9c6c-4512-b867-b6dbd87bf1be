# 客户信息查询工具

本项目提供了多种查询客户信息的工具，可以根据会员号查询客户的详细信息。

## 功能概览

1. **单个客户查询**：通过会员号查询单个客户的详细信息
2. **批量客户查询**：通过命令行参数批量查询多个客户信息
3. **Excel批量查询**：通过Excel文件批量查询客户信息并生成报告

## 使用前准备

安装必要的依赖：

```bash
pip install requests pandas openpyxl
```

## 工具使用说明

### 1. 单个客户查询 (api_request.py)

直接运行脚本，可以指定会员号参数：

```bash
python api_request.py [会员号]
```

如果不指定会员号，将使用默认会员号"00001053"进行查询。

示例：
```bash
python api_request.py 00001053
```

查询结果会显示在控制台，并将完整的API响应保存到`api_response.json`文件。

### 2. 批量客户查询 (batch_customer_query.py)

通过命令行参数指定多个会员号进行批量查询：

```bash
python batch_customer_query.py <会员号1> [会员号2] [会员号3] ...
```

示例：
```bash
python batch_customer_query.py 00001053 00001054
```

查询结果会显示在控制台，并将结果保存到`客户查询结果_时间戳.json`文件。

### 3. Excel批量查询 (excel_batch_processor.py 或 batch_info_processor.py)

#### 第一步：生成Excel模板

直接运行脚本，不带任何参数：

```bash
python excel_batch_processor.py
```

或

```bash
python batch_info_processor.py
```

这将生成一个名为`会员号模板.xlsx`的Excel文件。

#### 第二步：填写会员号

打开生成的Excel模板，在"会员号"列中填入要查询的会员号，可以填入多行进行批量查询。

#### 第三步：运行批量查询

使用填写好的Excel文件运行脚本：

```bash
python excel_batch_processor.py 会员号模板.xlsx
```

或

```bash
python batch_info_processor.py 会员号模板.xlsx
```

查询结果将保存到名为`客户信息报告_时间戳.xlsx`的Excel文件中，包含客户基本信息和标签信息。

## API说明

所有工具都使用以下API接口：

- **认证接口**：`https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login`
- **客户查询接口**：`https://em02-ym-openapi-admin.linkedcare.cn/api/v1/crm/customer`

查询参数：
- `keyword`：会员号

认证方式：JWT token

## 注意事项

1. 每次运行脚本将自动获取新的Token
2. 查询结果会保存在当前目录下的文件中
3. 如果某个会员号查询失败，会在控制台输出错误信息，但不会影响其他会员号的查询
4. 会员号"00001053"对应的客户是"诸葛建华"，可用于测试 