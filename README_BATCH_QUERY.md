# 客户信息批量高性能查询工具

这个工具用于批量高性能查询Excel文件中的会员号，获取对应的客户手机号，并将结果更新到Excel文件中。

## 功能特点

1. **批量处理**：按批次处理会员号，提高查询效率
2. **错误处理**：自动处理查询失败的情况，不影响其他会员号的查询
3. **数据类型兼容**：自动处理Excel中的数据类型转换
4. **限流保护**：添加适当延迟，避免API限流
5. **进度显示**：实时显示查询进度和结果

## 使用方法

### 基本用法

```bash
python update_mobile_numbers.py [Excel文件路径]
```

如果不指定Excel文件路径，将默认使用当前目录下的`hlb.xlsx`文件。

### 示例

```bash
python update_mobile_numbers.py hlb.xlsx
```

## Excel文件要求

1. Excel文件必须包含名为"会员号"的列
2. 如果已有"手机号"列，将更新该列的值；如果没有，将自动创建该列

## 处理流程

1. 读取Excel文件中的会员号列
2. 获取API认证Token
3. 按批次（默认每批10个）查询会员号对应的客户信息
4. 提取客户手机号
5. 将手机号更新到Excel文件中
6. 保存更新后的Excel文件（文件名格式：`hlb_updated_时间戳.xlsx`）

## 性能优化

1. **批量处理**：将会员号分批处理，减少API调用次数
2. **延迟控制**：
   - 每个API请求之间添加0.1秒延迟
   - 每批次之间添加1秒延迟
3. **数据预处理**：提前转换数据类型，避免处理过程中的类型转换警告

## 注意事项

1. 需要确保网络连接正常
2. API可能有调用频率限制，如遇到限流问题，可以调整批处理大小和延迟时间
3. 部分会员号可能查询不到对应的客户信息或手机号
4. 脚本会自动获取新的Token，无需手动提供

## 运行环境要求

- Python 3.6+
- 依赖库：pandas, openpyxl, requests

安装依赖：

```bash
pip install pandas openpyxl requests
```

## 示例输出

```
准备处理Excel文件: hlb.xlsx
=== 获取新的Token ===
请求URL: https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login
状态码: 201
Token获取成功!
Token: eyJ0eXAiOiJKV1QiLCJh...
正在读取Excel文件: hlb.xlsx
找到 119 个会员号，开始批量查询...
正在处理第 1-10/119 批会员号
  查询会员号: 00001053
  已获取会员号 00001053 的手机号: 18930532619
  ...
已更新 115/119 个会员号的手机号
更新后的文件已保存为: hlb_updated_20250705210620.xlsx
处理完成! 结果已保存至: hlb_updated_20250705210620.xlsx 