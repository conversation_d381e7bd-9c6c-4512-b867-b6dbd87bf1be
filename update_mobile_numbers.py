import pandas as pd
import requests
import json
import sys
import os
from datetime import datetime
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# 设置并发执行的最大工作线程数
MAX_WORKERS = 30  # 您可以根据需要调整此值

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def get_customer_info(keyword, token):
    """
    调用API接口获取客户信息
    
    Args:
        keyword (str): 查询关键字
        token (str): JWT令牌
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/crm/customer"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"查询会员号 {keyword} 失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"查询会员号 {keyword} 异常: {e}")
        return None

def extract_mobile_number(customer_data):
    """
    从API响应中提取手机号
    
    Args:
        customer_data (dict): API响应数据
        
    Returns:
        str: 提取的手机号，如果没有找到则返回空字符串
    """
    if not customer_data or "content" not in customer_data or not customer_data["content"]:
        return ""
    
    try:
        customer = customer_data["content"][0]  # 获取第一个客户
        return customer.get("mobile", "")
    except Exception as e:
        print(f"提取手机号时出错: {e}")
        return ""

def batch_query_customers(member_codes, token, batch_size=100):
    """
    通过并发方式批量查询客户信息

    Args:
        member_codes (list): 会员号列表
        token (str): JWT令牌
        batch_size (int): 批处理大小

    Returns:
        dict: 会员号到手机号的映射
    """
    result = {}
    total = len(member_codes)
    
    # 使用线程池进行并发处理
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 分批处理
        for i in range(0, total, batch_size):
            batch = member_codes[i:i+batch_size]
            print(f"正在处理第 {i+1}-{min(i+batch_size, total)}/{total} 批会员号")
            
            # 提交当前批次的所有任务
            future_to_code = {executor.submit(get_customer_info, code, token): code for code in batch if code and code.lower() != 'nan'}
            
            for future in as_completed(future_to_code):
                code = future_to_code[future]
                try:
                    customer_data = future.result()
                    if customer_data:
                        mobile = extract_mobile_number(customer_data)
                        if mobile:
                            result[code] = mobile
                            print(f"  已获取会员号 {code} 的手机号: {mobile}")
                        else:
                            print(f"  会员号 {code} 没有找到手机号")
                    else:
                        print(f"  查询会员号 {code} 失败")
                except Exception as exc:
                    print(f"  查询会员号 {code} 生成异常: {exc}")

            # 批次之间添加一个较短的延迟
            if i + batch_size < total:
                print(f"批次处理完成，等待0.25秒...")
                time.sleep(0.25)
    
    return result

def update_excel_with_mobile_numbers(excel_file, token):
    """
    更新Excel文件中的手机号
    
    Args:
        excel_file (str): Excel文件路径
        token (str): JWT令牌
        
    Returns:
        str: 更新后的Excel文件路径
    """
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_file}")
        df = pd.read_excel(excel_file)
        
        # 检查是否包含必要的列
        if '会员号' not in df.columns:
            print("错误: Excel文件必须包含'会员号'列")
            return None
        
        # 确保会员号列是字符串类型
        df['会员号'] = df['会员号'].astype(str)
        
        # 如果没有手机号列，则添加一个
        if '手机号' not in df.columns:
            df['手机号'] = ""
            print("已添加'手机号'列")
        else:
            # 确保手机号列是字符串类型
            df['手机号'] = df['手机号'].astype(str)
        
        # 获取会员号列表
        member_codes = df['会员号'].tolist()
        # 过滤掉空值和NaN
        member_codes = [code.strip() for code in member_codes if code and code.strip() and code.lower() != 'nan']
        
        if not member_codes:
            print("错误: 没有找到有效的会员号")
            return None
        
        print(f"找到 {len(member_codes)} 个会员号，开始批量查询...")
        
        # 批量查询会员号对应的手机号
        mobile_dict = batch_query_customers(member_codes, token)
        
        # 更新DataFrame中的手机号
        success_count = 0
        for index, row in df.iterrows():
            member_code = str(row['会员号']).strip()
            if not member_code or member_code.lower() == 'nan':
                continue
                
            if member_code in mobile_dict:
                df.at[index, '手机号'] = mobile_dict[member_code]
                success_count += 1
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        output_file = f"hlb_updated_{timestamp}.xlsx"
        
        # 保存更新后的Excel文件
        df.to_excel(output_file, index=False)
        print(f"\n已更新 {success_count}/{len(member_codes)} 个会员号的手机号")
        print(f"更新后的文件已保存为: {output_file}")
        
        return output_file
        
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    主函数
    """
    # 检查命令行参数
    excel_file = "hlb.xlsx"  # 默认文件名
    
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
    
    if not os.path.exists(excel_file):
        print(f"错误: 找不到文件 {excel_file}")
        return
    
    print(f"准备处理Excel文件: {excel_file}")
    
    # 获取token
    token = get_new_token()
    if not token:
        print("获取Token失败，无法继续操作")
        return
    
    # 更新Excel文件中的手机号
    output_file = update_excel_with_mobile_numbers(excel_file, token)
    
    if output_file:
        print(f"\n处理完成! 结果已保存至: {output_file}")
    else:
        print("\n处理失败，未能更新Excel文件")

if __name__ == "__main__":
    main() 