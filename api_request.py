import requests
import json
from pprint import pprint
import sys

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def call_customer_api(keyword="00001053", token=None):
    """
    调用API接口获取客户信息
    
    Args:
        keyword (str): 查询关键字，默认为"00001053"
        token (str): JWT令牌，如果为None则会尝试获取新的token
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # 如果没有提供token，尝试获取新的token
    if token is None:
        token = get_new_token()
        if not token:
            print("无法获取Token，无法继续API调用")
            return None
    
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/crm/customer"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print("=== 请求信息 ===")
    print(f"请求URL: {url}")
    print(f"请求参数: {params}")
    print(f"认证方式: x-access-token")
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 打印响应状态码
        print("\n=== 响应信息 ===")
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            print("请求成功!")
            return result
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def display_customer_result(data):
    """
    格式化显示API返回的客户信息结果
    
    Args:
        data (dict): API返回的JSON数据
    """
    if not data:
        print("\n没有获取到数据")
        return
    
    print("\n=== 客户信息 ===")
    
    if "content" in data and data["content"]:
        for item in data["content"]:
            print(f"\n客户姓名: {item.get('name')}")
            print(f"会员号: {item.get('number')}")
            print(f"手机号: {item.get('mobile') or '无'}")
            
            # 显示性别
            gender = item.get('gender')
            if gender == 'M':
                print("性别: 男")
            elif gender == 'F':
                print("性别: 女")
            else:
                print("性别: 未知")
            
            # 显示生日
            birthday = item.get('birthday')
            if birthday:
                print(f"生日: {birthday.get('year')}-{birthday.get('month'):02d}-{birthday.get('day'):02d}")
            else:
                print("生日: 未知")
                
            print(f"邮箱: {item.get('email') or '无'}")
            
            # 显示会员等级
            membership = item.get('membershipLevel', {})
            if membership:
                print(f"会员等级: {membership.get('name', '无')}")
            else:
                print("会员等级: 无")
            
            # 显示积分/余额
            print(f"积分余额: {item.get('creditBalance', 0)}")
            
            # 显示客户类型
            customer_type = item.get('type', {})
            if customer_type:
                print(f"客户类型: {customer_type.get('name', '无')}")
            
            # 显示地址信息
            address = item.get("address", {})
            if address:
                addr_parts = []
                if address.get('country'):
                    addr_parts.append(address.get('country'))
                if address.get('province'):
                    addr_parts.append(address.get('province'))
                if address.get('city'):
                    addr_parts.append(address.get('city'))
                if address.get('district'):
                    addr_parts.append(address.get('district'))
                if address.get('street'):
                    addr_parts.append(address.get('street'))
                
                if addr_parts:
                    print(f"地址: {' '.join(addr_parts)}")
                else:
                    print("地址: 无")
            else:
                print("地址: 无")
            
            # 显示顾问信息
            consultant = item.get('consultant', {})
            if consultant:
                print(f"顾问: {consultant.get('name', '无')}")
            
            # 显示医生信息
            doctor = item.get('doctor', {})
            if doctor:
                print(f"医生: {doctor.get('name', '无')}")
            
            # 显示客服信息
            service = item.get('customerService', {})
            if service:
                print(f"客服: {service.get('name', '无')}")
            
            # 显示来源渠道
            referrer = item.get('referrer', {})
            if referrer:
                print(f"来源渠道: {referrer.get('name', '无')}")
                
                channel = referrer.get('channel', {})
                if channel:
                    print(f"渠道类型: {channel.get('name', '无')}")
            
            # 显示所属机构
            org = item.get('organization', {})
            if org:
                print(f"所属机构: {org.get('name', '无')}")
            
            # 显示创建信息
            created_time = item.get('createdAt')
            if created_time:
                # 转换时间戳为日期时间
                from datetime import datetime
                dt = datetime.fromtimestamp(created_time/1000)
                print(f"创建时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
            
            created_by = item.get('createdBy', {})
            if created_by:
                print(f"创建人: {created_by.get('name', '无')}")
            
            # 显示标签信息
            profile_tags = item.get("profileTags", [])
            if profile_tags:
                tag_names = [tag.get('name', '') for tag in profile_tags]
                print(f"标签: {', '.join(tag_names)}")
            else:
                print("标签: 无")
            
            # 显示备注
            note = item.get('note')
            if note:
                print(f"备注: {note}")
    else:
        print("未找到客户信息")
    
    # 显示分页信息
    if "totalElements" in data:
        print(f"\n总记录数: {data.get('totalElements')}")
        print(f"总页数: {data.get('totalPages')}")

if __name__ == "__main__":
    # 检查是否有命令行参数作为会员号
    if len(sys.argv) > 1:
        keyword = sys.argv[1]
    else:
        keyword = "00001053"  # 默认会员号
    
    print(f"正在查询会员号: {keyword}")
    
    # 获取新的token
    token = get_new_token()
    
    if not token:
        print("获取Token失败，无法继续操作")
    else:
        # 调用API获取数据
        result = call_customer_api(keyword=keyword, token=token)
        
        # 显示格式化结果
        if result:
            display_customer_result(result)
            
            # 保存完整响应到文件
            with open("api_response.json", "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("\n完整响应已保存到 api_response.json 文件") 