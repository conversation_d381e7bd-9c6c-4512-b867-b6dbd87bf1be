import pandas as pd
import os
import sys
import json
import requests
import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def get_customer_info(keyword, token):
    """
    调用API接口获取客户信息
    
    Args:
        keyword (str): 查询关键字
        token (str): JWT令牌
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/crm/customer"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print(f"\n查询客户 {keyword}")
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"查询失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def extract_customer_info(customer_data):
    """
    从API响应中提取客户信息
    
    Args:
        customer_data (dict): API响应数据
        
    Returns:
        dict: 提取的客户信息，如果没有找到则返回None
    """
    if not customer_data or "content" not in customer_data or not customer_data["content"]:
        return None
    
    customer = customer_data["content"][0]  # 获取第一个客户
    
    # 基本信息
    info = {
        "id": customer.get("id", ""),
        "name": customer.get("name", ""),
        "code": customer.get("number", ""),
        "mobile": customer.get("mobile", ""),
        "gender": "男" if customer.get("gender") == "M" else "女" if customer.get("gender") == "F" else "未知",
        "tags": []
    }
    
    # 处理生日
    birthday = customer.get("birthday", {})
    if birthday:
        info["birthday"] = f"{birthday.get('year')}-{birthday.get('month'):02d}-{birthday.get('day'):02d}"
    else:
        info["birthday"] = ""
    
    # 处理邮箱
    info["email"] = customer.get("email", "")
    
    # 会员等级
    membership = customer.get("membershipLevel", {})
    if membership:
        info["membershipLevel"] = membership.get("name", "")
    else:
        info["membershipLevel"] = ""
    
    # 积分余额
    info["creditBalance"] = customer.get("creditBalance", 0)
    
    # 客户类型
    customer_type = customer.get("type", {})
    if customer_type:
        info["customerType"] = customer_type.get("name", "")
    else:
        info["customerType"] = ""
    
    # 地址信息
    address = customer.get("address", {})
    if address:
        addr_parts = []
        if address.get('country'):
            addr_parts.append(address.get('country'))
        if address.get('province'):
            addr_parts.append(address.get('province'))
        if address.get('city'):
            addr_parts.append(address.get('city'))
        if address.get('district'):
            addr_parts.append(address.get('district'))
        if address.get('street'):
            addr_parts.append(address.get('street'))
        
        if addr_parts:
            info["address"] = " ".join(addr_parts)
        else:
            info["address"] = ""
    else:
        info["address"] = ""
    
    # 顾问信息
    consultant = customer.get('consultant', {})
    if consultant:
        info["consultant"] = consultant.get('name', '')
    else:
        info["consultant"] = ""
    
    # 医生信息
    doctor = customer.get('doctor', {})
    if doctor:
        info["doctor"] = doctor.get('name', '')
    else:
        info["doctor"] = ""
    
    # 客服信息
    service = customer.get('customerService', {})
    if service:
        info["customerService"] = service.get('name', '')
    else:
        info["customerService"] = ""
    
    # 来源渠道
    referrer = customer.get('referrer', {})
    if referrer:
        info["referrer"] = referrer.get('name', '')
        
        channel = referrer.get('channel', {})
        if channel:
            info["channel"] = channel.get('name', '')
        else:
            info["channel"] = ""
    else:
        info["referrer"] = ""
        info["channel"] = ""
    
    # 所属机构
    org = customer.get('organization', {})
    if org:
        info["organization"] = org.get('name', '')
    else:
        info["organization"] = ""
    
    # 创建信息
    created_time = customer.get('createdAt')
    if created_time:
        from datetime import datetime
        dt = datetime.fromtimestamp(created_time/1000)
        info["createdAt"] = dt.strftime('%Y-%m-%d %H:%M:%S')
    else:
        info["createdAt"] = ""
    
    created_by = customer.get('createdBy', {})
    if created_by:
        info["createdBy"] = created_by.get('name', '')
    else:
        info["createdBy"] = ""
    
    # 备注
    info["note"] = customer.get('note', '')
    
    # 标签信息
    if "profileTags" in customer and customer["profileTags"]:
        for tag in customer["profileTags"]:
            tag_info = {
                "name": tag.get("name", ""),
                "code": tag.get("code", ""),
                "color": tag.get("color", "")
            }
            info["tags"].append(tag_info)
    
    return info

def create_excel_template():
    """
    创建Excel模板文件
    
    Returns:
        str: 创建的Excel文件路径
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "会员号"
    
    # 设置列宽
    ws.column_dimensions['A'].width = 20
    ws.column_dimensions['B'].width = 40
    
    # 设置标题样式
    title_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    title_fill = PatternFill(fill_type='solid', fgColor='4472C4')
    title_alignment = Alignment(horizontal='center', vertical='center')
    title_border = Border(
        left=Side(style='thin'), 
        right=Side(style='thin'), 
        top=Side(style='thin'), 
        bottom=Side(style='thin')
    )
    
    # 添加标题
    ws['A1'] = '序号'
    ws['B1'] = '会员号'
    
    for cell in ws[1]:
        cell.font = title_font
        cell.fill = title_fill
        cell.alignment = title_alignment
        cell.border = title_border
    
    # 添加示例数据
    ws['A2'] = 1
    ws['B2'] = '00001053'
    
    # 添加说明
    ws['A4'] = '说明：'
    ws['A5'] = '1. 在"会员号"列填入要查询的会员号'
    ws['A6'] = '2. 可以填入多行会员号进行批量查询'
    ws['A7'] = '3. 运行脚本后将生成结果Excel文件'
    
    # 保存模板
    template_path = "会员号模板.xlsx"
    wb.save(template_path)
    print(f"Excel模板已创建: {template_path}")
    return template_path

def process_excel_and_generate_report(input_file, token):
    """
    处理Excel文件并生成报告
    
    Args:
        input_file (str): 输入Excel文件路径
        token (str): JWT令牌
        
    Returns:
        str: 生成的报告文件路径
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(input_file)
        
        # 检查是否包含必要的列
        if '会员号' not in df.columns:
            print("错误: Excel文件必须包含'会员号'列")
            return None
        
        # 提取会员号列
        codes = df['会员号'].astype(str).tolist()
        codes = [code for code in codes if code and code.strip() and code.lower() != 'nan']
        
        if not codes:
            print("错误: 没有找到有效的会员号")
            return None
        
        print(f"找到 {len(codes)} 个会员号，开始处理...")
        
        # 收集客户信息
        customers_info = []
        tags_info = []
        
        for code in codes:
            # 查询客户信息
            customer_data = get_customer_info(code, token)
            
            if customer_data:
                # 提取客户信息
                info = extract_customer_info(customer_data)
                
                if info:
                    customers_info.append({
                        "id": info["id"],
                        "name": info["name"],
                        "code": info["code"],
                        "mobile": info["mobile"],
                        "gender": info["gender"],
                        "birthday": info["birthday"],
                        "email": info["email"],
                        "membershipLevel": info["membershipLevel"],
                        "creditBalance": info["creditBalance"],
                        "customerType": info["customerType"],
                        "address": info["address"],
                        "consultant": info["consultant"],
                        "doctor": info["doctor"],
                        "customerService": info["customerService"],
                        "referrer": info["referrer"],
                        "channel": info["channel"],
                        "organization": info["organization"],
                        "createdAt": info["createdAt"],
                        "createdBy": info["createdBy"],
                        "note": info["note"],
                        "tagsCount": len(info["tags"])
                    })
                    
                    # 添加标签信息
                    for tag in info["tags"]:
                        tags_info.append({
                            "customerId": info["id"],
                            "customerName": info["name"],
                            "customerCode": info["code"],
                            "tagName": tag["name"],
                            "tagCode": tag["code"],
                            "tagColor": tag["color"]
                        })
                else:
                    print(f"未找到客户 {code} 的信息")
            else:
                print(f"查询客户 {code} 失败")
        
        if not customers_info:
            print("没有找到任何客户信息")
            return None
        
        # 生成Excel报告
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        output_file = f"客户信息报告_{timestamp}.xlsx"
        
        with pd.ExcelWriter(output_file) as writer:
            # 客户信息表
            pd.DataFrame(customers_info).to_excel(writer, sheet_name="客户信息", index=False)
            
            # 标签信息表
            if tags_info:
                pd.DataFrame(tags_info).to_excel(writer, sheet_name="标签信息", index=False)
        
        print(f"报告已生成: {output_file}")
        print(f"共处理 {len(codes)} 个客户，找到 {len(customers_info)} 个客户信息，{len(tags_info)} 个标签信息")
        
        return output_file
        
    except Exception as e:
        print(f"处理Excel文件时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    # 检查命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        if not os.path.exists(input_file):
            print(f"错误: 找不到文件 {input_file}")
            return
    else:
        # 创建Excel模板
        input_file = create_excel_template()
        print(f"已创建Excel模板: {input_file}")
        print("请在模板中填入会员号后再次运行此脚本")
        print(f"用法: python {sys.argv[0]} <Excel文件路径>")
        return
    
    # 获取token
    token = get_new_token()
    if not token:
        print("获取Token失败，无法继续操作")
        return
    
    # 处理Excel文件并生成报告
    output_file = process_excel_and_generate_report(input_file, token)
    
    if output_file:
        print(f"\n处理完成! 结果已保存至: {output_file}")
    else:
        print("\n处理失败，未能生成报告")

if __name__ == "__main__":
    main() 