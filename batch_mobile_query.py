import pandas as pd
import requests
import json
from datetime import datetime
import time

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def get_customer_info(mobile, token):
    """
    根据手机号调用API接口获取客户信息
    
    Args:
        mobile (str): 手机号
        token (str): JWT令牌
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/crm/customer"
    
    # 请求参数
    params = {
        "keyword": mobile
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"查询手机号 {mobile} 失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"查询手机号 {mobile} 请求异常: {e}")
        return None

def extract_customer_summary(customer_data, mobile):
    """
    从API响应中提取客户摘要信息
    
    Args:
        customer_data (dict): API响应数据
        mobile (str): 查询的手机号
        
    Returns:
        list: 客户信息列表，每个客户一个字典
    """
    if not customer_data or "content" not in customer_data or not customer_data["content"]:
        return [{
            "手机号": mobile,
            "会员号": "未找到",
            "姓名": "未找到",
            "成交状态": "未找到",
            "会员等级": "未找到",
            "积分余额": 0,
            "所属机构": "未找到",
            "创建时间": "未找到"
        }]
    
    results = []
    for customer in customer_data["content"]:
        # 判断成交状态 - 根据标签判断
        profile_tags = customer.get("profileTags", [])
        tag_names = [tag.get('name', '') for tag in profile_tags]
        
        # 判断成交状态
        if any("成交" in tag for tag in tag_names):
            deal_status = "已成交"
        elif any("未成交" in tag for tag in tag_names):
            deal_status = "未成交"
        else:
            # 根据会员等级判断，非会员通常是未成交
            membership = customer.get("membershipLevel", {})
            if membership and membership.get("name") != "非会员":
                deal_status = "已成交"
            else:
                deal_status = "未成交"
        
        # 会员等级
        membership = customer.get("membershipLevel", {})
        membership_level = membership.get("name", "无") if membership else "无"
        
        # 所属机构
        org = customer.get('organization', {})
        organization = org.get('name', '无') if org else '无'
        
        # 创建时间
        created_time = customer.get('createdAt')
        if created_time:
            dt = datetime.fromtimestamp(created_time/1000)
            created_at = dt.strftime('%Y-%m-%d %H:%M:%S')
        else:
            created_at = "未知"
        
        result = {
            "手机号": mobile,
            "会员号": customer.get("number", ""),
            "姓名": customer.get("name", ""),
            "成交状态": deal_status,
            "会员等级": membership_level,
            "积分余额": customer.get("creditBalance", 0),
            "所属机构": organization,
            "创建时间": created_at
        }
        results.append(result)
    
    return results

def process_mobile_batch_query(input_file):
    """
    批量处理手机号查询
    
    Args:
        input_file (str): 输入Excel文件路径
        
    Returns:
        str: 生成的结果文件路径
    """
    try:
        # 读取Excel文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_excel(input_file)
        
        print(f"文件列名: {df.columns.tolist()}")
        print(f"数据形状: {df.shape}")
        
        # 查找包含手机号的列
        mobile_column = None
        for col in df.columns:
            if "手机" in str(col) or "电话" in str(col) or "mobile" in str(col).lower():
                mobile_column = col
                break
        
        if mobile_column is None:
            # 如果没有找到明确的手机号列，尝试第一列
            mobile_column = df.columns[0]
            print(f"未找到明确的手机号列，使用第一列: {mobile_column}")
        else:
            print(f"找到手机号列: {mobile_column}")
        
        # 提取手机号列表
        mobiles = df[mobile_column].astype(str).tolist()
        # 过滤掉空值和无效值
        mobiles = [mobile.strip() for mobile in mobiles if mobile and mobile.strip() and mobile.lower() != 'nan']
        
        if not mobiles:
            print("错误: 没有找到有效的手机号")
            return None
        
        print(f"找到 {len(mobiles)} 个手机号，开始批量查询...")
        
        # 获取token
        token = get_new_token()
        if not token:
            print("获取Token失败，无法继续操作")
            return None
        
        # 批量查询客户信息
        all_results = []
        
        for i, mobile in enumerate(mobiles, 1):
            print(f"\n[{i}/{len(mobiles)}] 查询手机号: {mobile}")
            
            # 调用API获取客户信息
            customer_data = get_customer_info(mobile, token)
            
            # 提取客户摘要信息
            customer_summaries = extract_customer_summary(customer_data, mobile)
            all_results.extend(customer_summaries)
            
            # 添加延时避免请求过快
            time.sleep(0.5)
        
        if not all_results:
            print("错误: 没有获取到任何客户信息")
            return None
        
        # 创建结果DataFrame
        result_df = pd.DataFrame(all_results)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"客户查询结果_{timestamp}.xlsx"
        
        # 保存到Excel文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            result_df.to_excel(writer, sheet_name='客户信息', index=False)
            
            # 获取工作表对象进行格式化
            worksheet = writer.sheets['客户信息']
            
            # 设置列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"\n批量查询完成！")
        print(f"共查询 {len(mobiles)} 个手机号")
        print(f"获得 {len(all_results)} 条客户记录")
        print(f"结果已保存到: {output_file}")
        
        return output_file
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    input_file = "kh.xlsx"
    process_mobile_batch_query(input_file)
