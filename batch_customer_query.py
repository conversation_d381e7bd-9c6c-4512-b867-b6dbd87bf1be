import requests
import json
import sys
from datetime import datetime

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def get_customer_info(keyword, token):
    """
    调用API接口获取客户信息
    
    Args:
        keyword (str): 查询关键字
        token (str): JWT令牌
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/crm/customer"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print(f"\n查询客户 {keyword}")
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"查询失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def extract_customer_info(customer_data):
    """
    从API响应中提取客户信息
    
    Args:
        customer_data (dict): API响应数据
        
    Returns:
        dict: 提取的客户信息，如果没有找到则返回None
    """
    if not customer_data or "content" not in customer_data or not customer_data["content"]:
        return None
    
    customer = customer_data["content"][0]  # 获取第一个客户
    
    # 基本信息
    info = {
        "id": customer.get("id", ""),
        "name": customer.get("name", ""),
        "code": customer.get("number", ""),
        "mobile": customer.get("mobile", ""),
        "gender": "男" if customer.get("gender") == "M" else "女" if customer.get("gender") == "F" else "未知",
    }
    
    # 处理生日
    birthday = customer.get("birthday", {})
    if birthday:
        info["birthday"] = f"{birthday.get('year')}-{birthday.get('month'):02d}-{birthday.get('day'):02d}"
    else:
        info["birthday"] = "未知"
    
    # 处理邮箱
    info["email"] = customer.get("email") or "无"
    
    # 会员等级
    membership = customer.get("membershipLevel", {})
    if membership:
        info["membershipLevel"] = membership.get("name", "")
    else:
        info["membershipLevel"] = "无"
    
    # 积分余额
    info["creditBalance"] = customer.get("creditBalance", 0)
    
    # 客户类型
    customer_type = customer.get("type", {})
    if customer_type:
        info["customerType"] = customer_type.get("name", "")
    else:
        info["customerType"] = "无"
    
    # 地址信息
    address = customer.get("address", {})
    if address:
        addr_parts = []
        if address.get('country'):
            addr_parts.append(address.get('country'))
        if address.get('province'):
            addr_parts.append(address.get('province'))
        if address.get('city'):
            addr_parts.append(address.get('city'))
        if address.get('district'):
            addr_parts.append(address.get('district'))
        if address.get('street'):
            addr_parts.append(address.get('street'))
        
        if addr_parts:
            info["address"] = " ".join(addr_parts)
        else:
            info["address"] = "无"
    else:
        info["address"] = "无"
    
    # 顾问信息
    consultant = customer.get('consultant', {})
    if consultant:
        info["consultant"] = consultant.get('name', '')
    else:
        info["consultant"] = "无"
    
    # 医生信息
    doctor = customer.get('doctor', {})
    if doctor:
        info["doctor"] = doctor.get('name', '')
    else:
        info["doctor"] = "无"
    
    # 客服信息
    service = customer.get('customerService', {})
    if service:
        info["customerService"] = service.get('name', '')
    else:
        info["customerService"] = "无"
    
    # 所属机构
    org = customer.get('organization', {})
    if org:
        info["organization"] = org.get('name', '')
    else:
        info["organization"] = "无"
    
    # 创建信息
    created_time = customer.get('createdAt')
    if created_time:
        dt = datetime.fromtimestamp(created_time/1000)
        info["createdAt"] = dt.strftime('%Y-%m-%d %H:%M:%S')
    else:
        info["createdAt"] = "未知"
    
    # 标签信息
    profile_tags = customer.get("profileTags", [])
    if profile_tags:
        tag_names = [tag.get('name', '') for tag in profile_tags]
        info["tags"] = ", ".join(tag_names)
    else:
        info["tags"] = "无"
    
    # 备注
    info["note"] = customer.get('note') or "无"
    
    return info

def display_customer_info(info):
    """
    显示客户信息
    
    Args:
        info (dict): 客户信息字典
    """
    print("\n" + "=" * 40)
    print(f"客户姓名: {info['name']}")
    print(f"会员号: {info['code']}")
    print(f"手机号: {info['mobile']}")
    print(f"性别: {info['gender']}")
    print(f"生日: {info['birthday']}")
    print(f"邮箱: {info['email']}")
    print(f"会员等级: {info['membershipLevel']}")
    print(f"积分余额: {info['creditBalance']}")
    print(f"客户类型: {info['customerType']}")
    print(f"地址: {info['address']}")
    print(f"顾问: {info['consultant']}")
    print(f"医生: {info['doctor']}")
    print(f"客服: {info['customerService']}")
    print(f"所属机构: {info['organization']}")
    print(f"创建时间: {info['createdAt']}")
    print(f"标签: {info['tags']}")
    print(f"备注: {info['note']}")
    print("=" * 40)

def main():
    """
    主函数
    """
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("用法: python batch_customer_query.py <会员号1> [会员号2] [会员号3] ...")
        print("示例: python batch_customer_query.py 00001053 00001054")
        return
    
    # 获取会员号列表
    member_codes = sys.argv[1:]
    
    print(f"准备查询 {len(member_codes)} 个会员信息")
    
    # 获取token
    token = get_new_token()
    if not token:
        print("获取Token失败，无法继续操作")
        return
    
    # 查询每个会员信息
    results = []
    for code in member_codes:
        # 调用API获取数据
        data = get_customer_info(code, token)
        
        if data:
            # 提取客户信息
            info = extract_customer_info(data)
            
            if info:
                # 显示客户信息
                display_customer_info(info)
                results.append(info)
            else:
                print(f"未找到会员号 {code} 的客户信息")
        else:
            print(f"查询会员号 {code} 失败")
    
    # 显示统计信息
    print(f"\n共查询 {len(member_codes)} 个会员号，成功获取 {len(results)} 个客户信息")
    
    # 保存结果到JSON文件
    if results:
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        output_file = f"客户查询结果_{timestamp}.json"
        
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"查询结果已保存到 {output_file}")

if __name__ == "__main__":
    main() 