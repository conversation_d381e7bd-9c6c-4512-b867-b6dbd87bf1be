import pandas as pd
import os
import sys
import json
import requests
import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def get_customer_info(keyword, token):
    """
    调用API接口获取客户信息
    
    Args:
        keyword (str): 查询关键字
        token (str): JWT令牌
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/crm/customer"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print(f"\n查询客户 {keyword}")
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"查询失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def extract_customer_info(customer_data):
    """
    从API响应中提取客户信息
    
    Args:
        customer_data (dict): API响应数据
        
    Returns:
        dict: 提取的客户信息，如果没有找到则返回None
    """
    if not customer_data or "content" not in customer_data or not customer_data["content"]:
        return None
    
    customer = customer_data["content"][0]  # 获取第一个客户
    
    # 基本信息
    info = {
        "id": customer.get("id", ""),
        "name": customer.get("name", ""),
        "code": customer.get("code", ""),
        "mobile": customer.get("mobile", ""),
        "gender": customer.get("gender", ""),
        "birthday": customer.get("birthday", ""),
        "email": customer.get("email", ""),
        "tags": []
    }
    
    # 地址信息
    address = customer.get("address", {})
    if address:
        info["province"] = address.get("province", "")
        info["city"] = address.get("city", "")
        info["district"] = address.get("district", "")
        info["addressDetail"] = address.get("detail", "")
    else:
        info["province"] = ""
        info["city"] = ""
        info["district"] = ""
        info["addressDetail"] = ""
    
    # 标签信息
    if "tags" in customer and customer["tags"]:
        for tag in customer["tags"]:
            tag_info = {
                "id": tag.get("id", ""),
                "name": tag.get("name", ""),
                "color": tag.get("color", "")
            }
            info["tags"].append(tag_info)
    
    return info

def create_excel_template():
    """
    创建Excel模板文件
    
    Returns:
        str: 创建的Excel文件路径
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "会员号"
    
    # 设置列宽
    ws.column_dimensions['A'].width = 20
    ws.column_dimensions['B'].width = 40
    
    # 设置标题样式
    title_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    title_fill = PatternFill(fill_type='solid', fgColor='4472C4')
    title_alignment = Alignment(horizontal='center', vertical='center')
    title_border = Border(
        left=Side(style='thin'), 
        right=Side(style='thin'), 
        top=Side(style='thin'), 
        bottom=Side(style='thin')
    )
    
    # 添加标题
    ws['A1'] = '序号'
    ws['B1'] = '会员号'
    
    for cell in ws[1]:
        cell.font = title_font
        cell.fill = title_fill
        cell.alignment = title_alignment
        cell.border = title_border
    
    # 添加示例数据
    ws['A2'] = 1
    ws['B2'] = '00001053'  # 确保会员号为文本格式
    
    # 设置B列为文本格式
    from openpyxl.styles import numbers
    for row in range(2, 100):  # 预设100行
        cell = ws.cell(row=row, column=2)
        cell.number_format = numbers.FORMAT_TEXT
    
    # 添加说明
    ws['A4'] = '说明：'
    ws['A5'] = '1. 在"会员号"列填入要查询的会员号'
    ws['A6'] = '2. 可以填入多行会员号进行批量查询'
    ws['A7'] = '3. 会员号会被当作文本处理，请确保输入的是完整的编码'
    ws['A8'] = '4. 运行脚本后将生成结果Excel文件'
    
    # 保存模板
    template_path = "会员号模板.xlsx"
    wb.save(template_path)
    print(f"Excel模板已创建: {template_path}")
    return template_path

def process_excel_and_generate_report(input_file, token):
    """
    处理Excel文件并生成报告
    
    Args:
        input_file (str): 输入Excel文件路径
        token (str): JWT令牌
        
    Returns:
        str: 生成的报告文件路径
    """
    try:
        # 读取Excel文件，确保会员号列为文本格式
        df = pd.read_excel(input_file, dtype={'会员号': str})
        
        # 检查是否包含必要的列
        if '会员号' not in df.columns:
            print("错误: Excel文件必须包含'会员号'列")
            return None
        
        # 提取会员号列，并确保是字符串类型
        codes = df['会员号'].astype(str).tolist()
        # 过滤掉空值和NaN
        codes = [code.strip() for code in codes if code and code.strip() and code.lower() != 'nan']
        # 去掉可能的小数点和零
        codes = [code.split('.')[0] if '.' in code else code for code in codes]
        
        if not codes:
            print("错误: 没有找到有效的会员号")
            return None
        
        print(f"找到 {len(codes)} 个会员号，开始处理...")
        
        # 收集客户信息
        customers_info = []
        tags_info = []
        
        for code in codes:
            # 调用API获取客户信息
            customer_data = get_customer_info(code, token)
            
            if not customer_data:
                print(f"警告: 无法获取会员号 {code} 的信息，跳过")
                continue
                
            # 提取客户信息
            info = extract_customer_info(customer_data)
            
            if not info:
                print(f"警告: 会员号 {code} 未找到客户信息，跳过")
                continue
                
            # 添加到客户信息列表
            customers_info.append({
                "id": info["id"],
                "name": info["name"],
                "code": info["code"],
                "mobile": info["mobile"],
                "gender": info["gender"],
                "birthday": info["birthday"],
                "email": info["email"],
                "province": info["province"],
                "city": info["city"],
                "district": info["district"],
                "addressDetail": info["addressDetail"],
                "tagsCount": len(info["tags"])
            })
            
            # 添加标签信息
            for tag in info["tags"]:
                tags_info.append({
                    "customerId": info["id"],
                    "customerName": info["name"],
                    "customerCode": info["code"],
                    "tagId": tag["id"],
                    "tagName": tag["name"],
                    "tagColor": tag["color"]
                })
        
        if not customers_info:
            print("错误: 没有获取到任何客户信息")
            return None
            
        # 创建时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        
        # 创建Excel报告
        report_path = f"客户信息报告_{timestamp}.xlsx"
        
        # 创建DataFrame
        df_customers = pd.DataFrame(customers_info)
        df_tags = pd.DataFrame(tags_info)
        
        # 保存到Excel
        with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
            df_customers.to_excel(writer, sheet_name='客户信息', index=False)
            if not df_tags.empty:
                df_tags.to_excel(writer, sheet_name='标签信息', index=False)
        
        print(f"\n报告已生成: {report_path}")
        return report_path
        
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        return None

def main():
    # 检查命令行参数
    if len(sys.argv) == 1:
        # 没有参数，创建模板
        create_excel_template()
    elif len(sys.argv) == 2:
        # 有一个参数，处理Excel文件
        input_file = sys.argv[1]
        
        if not os.path.exists(input_file):
            print(f"错误: 文件 '{input_file}' 不存在")
            return
            
        # 获取新的token
        token = get_new_token()
        
        if not token:
            print("错误: 无法获取token，请检查网络连接或API状态")
            return
            
        # 处理Excel文件并生成报告
        process_excel_and_generate_report(input_file, token)
    else:
        print("用法: python excel_batch_processor.py [excel文件路径]")

if __name__ == "__main__":
    main() 